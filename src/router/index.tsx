import type React from 'react';
import { lazy, Suspense } from 'react';
import {
  createBrowserRouter,
  RouterProvider,
  type RouteObject,
} from "react-router";
import Loading from '@/components/Loading';
import PageLayout from '@/layouts/PageLayout';
import ProtectedRoute from './ProtectedRoute';

// 使用懒加载替代直接导入
const StudentLogin = lazy(() => import('@/pages/login/sutdent'));
const ProcessPage = lazy(() => import('@/pages/process'));
const AccountPage = lazy(() => import('@/pages/account'));

function createSuspenseElement(element: React.ReactNode, isProtected = false) {
  if (isProtected) {
    return (
      <ProtectedRoute>
        <Suspense fallback={<Loading />}>
          {element}
        </Suspense>
      </ProtectedRoute>
    )
  }
  return (
    <Suspense fallback={<Loading />}>
      {element}
    </Suspense>
  )
}

const publicRoutes: Array<RouteObject> = [
  {
    path: "/login/student",
    element: createSuspenseElement(<StudentLogin />),
  },
  {
    path: "/",
    element: <PageLayout />,
    children: [
      {
        path: "/",
        element: createSuspenseElement(<ProcessPage />, true)
      },
      {
        path: "account",
        element: createSuspenseElement(<AccountPage />, true)
      },
    ],
  },
];

const Router: React.FC = () => {
  const router = createBrowserRouter([...publicRoutes], {
    basename: import.meta.env.VITE_APP_BASE_PATH
  });
  // 可以在这里动态控制路由，比如通过接口内容来控制路由
  return (
    <RouterProvider router={router} />
  )
}

export default Router;
